{"extends": "@repo/typescript-config/nextjs.json", "compilerOptions": {"lib": ["ES2022", "dom", "dom.iterable"], "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@repo/web-ui/*": ["../../packages/web-ui/src/*"]}, "plugins": [{"name": "next"}], "module": "esnext"}, "include": [".", "next-env.d.ts", "next.config.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}