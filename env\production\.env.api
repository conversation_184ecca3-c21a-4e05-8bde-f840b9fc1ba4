# ===========================================
# 生产环境 - API配置 (仅限api项目可访问)
# ===========================================


# 数据库配置
DATABASE_URI=

# 阿里云配置
ALI_ACCESS_KEY=
ALI_ACCESSKEY_SECRET=
ARN=

# 域名配置
DOMAIN_NAME=
NEXT_PUBLIC_WEB_HOST=

# S3配置
s3_NEDPOINT=http://localhost:9000
s3_ACCESS_KEY=root
s3_ACCESS_SECRET="G?cue>NY=D+p%QEuc#5g"

# Redis配置
REDIS_CLIENT_HOST=
REDIS_CLIENT_PORT=
REDIS_CLIENT_DB=

# 认证配置
BETTER_AUTH_SECRET=
BETTER_AUTH_URL=
TRUSTED_ORIGINS=
CROSS_DOMAIN_ORIGIN=
EMAIL_VERIFICATION_PAGE_PATH=
PASSWORD_RESET_PAGE_PATH=

# 邮件配置
MAIL_HOST=
MAIL_PORT=
MAIL_USER=
MAIL_PASS=
MAIL_FROM_NAME=

# 允许的IP地址列表（逗号分隔，可选）
# INTERNAL_ALLOWED_IPS=*************,*********

# 是否允许本地IP访问（默认: true）
INTERNAL_ALLOW_LOCAL_IPS=true

# 是否允许私有网络IP访问（默认: true）
INTERNAL_ALLOW_PRIVATE_IPS=true

# 是否需要内部API密钥验证（默认: false）
INTERNAL_REQUIRE_API_KEY=false

# 内部API密钥（当启用API密钥验证时设置）
# INTERNAL_API_KEY=your-secret-internal-key

# 内部API密钥环境变量名（默认: INTERNAL_API_KEY）
# INTERNAL_API_KEY_ENV_VAR=INTERNAL_API_KEY

# API配置
NEXT_PUBLIC_API_URL=

# GitHub OAuth配置
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# 微信OAuth配置
WECHAT_CLIENT_ID=
WECHAT_CLIENT_SECRET=

# MeiliSearch 配置(全文搜索)
MEILI_HOST=
MEILI_MASTER_KEY=

# 腾讯地图密钥

TMAP_KEY=
TMAP_SECRET=