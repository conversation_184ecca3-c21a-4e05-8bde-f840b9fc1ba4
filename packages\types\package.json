{"name": "@repo/types", "version": "0.0.0", "private": true, "description": "Shared TypeScript type definitions", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint src/", "type-check": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "^9.31.0", "tsup": "^8.0.0", "typescript": "^5.8.0"}, "publishConfig": {"access": "public"}}