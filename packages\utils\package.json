{"name": "@repo/utils", "version": "0.0.0", "private": true, "description": "Shared utility functions", "exports": {"./api-client": "./src/api-client.ts"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint src/", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/types": "workspace:*"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "eslint": "^9.31.0", "tsup": "^8.0.0", "typescript": "^5.8.0"}, "publishConfig": {"access": "public"}}