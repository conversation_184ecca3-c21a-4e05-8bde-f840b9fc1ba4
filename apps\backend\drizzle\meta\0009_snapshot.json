{"id": "1531edb9-fc2c-41f9-836f-aa969b945648", "prevId": "4188b39b-1a78-4fdf-b1a8-a1ef08dfe2ea", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"accounts_id_unique": {"name": "accounts_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_id_unique": {"name": "sessions_id_unique", "nullsNotDistinct": false, "columns": ["id"]}, "sessions_token_unique": {"name": "sessions_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "''"}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "phone_number_verified": {"name": "phone_number_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "''"}, "sex": {"name": "sex", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'customer'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "''"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_users_email_active": {"name": "idx_users_email_active", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "is_active = true", "concurrently": false, "method": "btree", "with": {}}, "idx_users_phone_active": {"name": "idx_users_phone_active", "columns": [{"expression": "phone_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "phone_number IS NOT NULL AND is_active = true", "concurrently": false, "method": "btree", "with": {}}, "idx_users_role_active": {"name": "idx_users_role_active", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_id_unique": {"name": "users_id_unique", "nullsNotDistinct": false, "columns": ["id"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_phone_number_unique": {"name": "users_phone_number_unique", "nullsNotDistinct": false, "columns": ["phone_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verifications": {"name": "verifications", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"verifications_id_unique": {"name": "verifications_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_profiles": {"name": "user_profiles", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "real_name": {"name": "real_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "id_card_number": {"name": "id_card_number", "type": "<PERSON><PERSON><PERSON>(18)", "primaryKey": false, "notNull": false}, "face_recognition_data": {"name": "face_recognition_data", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_profiles_user_id_users_id_fk": {"name": "user_profiles_user_id_users_id_fk", "tableFrom": "user_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_profiles_id_card_number_unique": {"name": "user_profiles_id_card_number_unique", "nullsNotDistinct": false, "columns": ["id_card_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_personnel": {"name": "service_personnel", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "shop_id": {"name": "shop_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "years_of_experience": {"name": "years_of_experience", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "work_start_time": {"name": "work_start_time", "type": "time", "primaryKey": false, "notNull": false}, "work_end_time": {"name": "work_end_time", "type": "time", "primaryKey": false, "notNull": false}, "is_available": {"name": "is_available", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {"idx_service_personnel_available": {"name": "idx_service_personnel_available", "columns": [{"expression": "is_available", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "shop_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "is_available = true", "concurrently": false, "method": "btree", "with": {}}, "idx_service_personnel_shop_available": {"name": "idx_service_personnel_shop_available", "columns": [{"expression": "shop_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_available", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "shop_id IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "idx_service_personnel_work_time": {"name": "idx_service_personnel_work_time", "columns": [{"expression": "work_start_time", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "work_end_time", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_available", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "is_available = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"service_personnel_user_id_users_id_fk": {"name": "service_personnel_user_id_users_id_fk", "tableFrom": "service_personnel", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "service_personnel_shop_id_shops_id_fk": {"name": "service_personnel_shop_id_shops_id_fk", "tableFrom": "service_personnel", "tableTo": "shops", "columnsFrom": ["shop_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"service_personnel_user_id_unique": {"name": "service_personnel_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.shops": {"name": "shops", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "owner_id": {"name": "owner_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "home_number": {"name": "home_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "province": {"name": "province", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "district": {"name": "district", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "county": {"name": "county", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "geom": {"name": "geom", "type": "geometry(point)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_shops_location": {"name": "idx_shops_location", "columns": [{"expression": "geom", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "geom IS NOT NULL", "concurrently": false, "method": "gist", "with": {}}, "idx_shops_owner_active": {"name": "idx_shops_owner_active", "columns": [{"expression": "owner_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_shops_province": {"name": "idx_shops_province", "columns": [{"expression": "province", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_shops_district": {"name": "idx_shops_district", "columns": [{"expression": "district", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_shops_county": {"name": "idx_shops_county", "columns": [{"expression": "county", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_shops_geo_hierarchy": {"name": "idx_shops_geo_hierarchy", "columns": [{"expression": "province", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "district", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "county", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"shops_owner_id_users_id_fk": {"name": "shops_owner_id_users_id_fk", "tableFrom": "shops", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"shops_id_unique": {"name": "shops_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_categories": {"name": "service_categories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_service_categories_parent": {"name": "idx_service_categories_parent", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "parent_id IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "idx_service_categories_name": {"name": "idx_service_categories_name", "columns": [{"expression": "name gin_trgm_ops", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gin", "with": {}}}, "foreignKeys": {"fk_sc_parent": {"name": "fk_sc_parent", "tableFrom": "service_categories", "tableTo": "service_categories", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"service_categories_id_unique": {"name": "service_categories_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_personnel_skills": {"name": "service_personnel_skills", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "service_id": {"name": "service_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {"idx_service_personnel_skills_service": {"name": "idx_service_personnel_skills_service", "columns": [{"expression": "service_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_service_personnel_skills_user": {"name": "idx_service_personnel_skills_user", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "service_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"service_personnel_skills_user_id_service_personnel_user_id_fk": {"name": "service_personnel_skills_user_id_service_personnel_user_id_fk", "tableFrom": "service_personnel_skills", "tableTo": "service_personnel", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "cascade", "onUpdate": "no action"}, "service_personnel_skills_service_id_services_id_fk": {"name": "service_personnel_skills_service_id_services_id_fk", "tableFrom": "service_personnel_skills", "tableTo": "services", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"service_personnel_skills_pkey": {"name": "service_personnel_skills_pkey", "columns": ["user_id", "service_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.services": {"name": "services", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "base_price": {"name": "base_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "estimated_duration_minutes": {"name": "estimated_duration_minutes", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {"idx_services_category_active": {"name": "idx_services_category_active", "columns": [{"expression": "category_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "base_price", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "is_active = true", "concurrently": false, "method": "btree", "with": {}}, "idx_services_price_range": {"name": "idx_services_price_range", "columns": [{"expression": "base_price", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "is_active = true", "concurrently": false, "method": "btree", "with": {}}, "idx_services_name_search": {"name": "idx_services_name_search", "columns": [{"expression": "name gin_trgm_ops", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "where": "is_active = true", "concurrently": false, "method": "gin", "with": {}}, "idx_services_duration": {"name": "idx_services_duration", "columns": [{"expression": "estimated_duration_minutes", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "is_active = true AND estimated_duration_minutes IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"services_category_id_service_categories_id_fk": {"name": "services_category_id_service_categories_id_fk", "tableFrom": "services", "tableTo": "service_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"services_id_unique": {"name": "services_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_addresses": {"name": "user_addresses", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "detailedAddress": {"name": "detailed<PERSON>ddress", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "home_number": {"name": "home_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "province": {"name": "province", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "district": {"name": "district", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "geom": {"name": "geom", "type": "geometry(point)", "primaryKey": false, "notNull": false}, "recipient_name": {"name": "recipient_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "sex": {"name": "sex", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "recipient_phone": {"name": "recipient_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"idx_user_addresses_location": {"name": "idx_user_addresses_location", "columns": [{"expression": "geom", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "geom IS NOT NULL", "concurrently": false, "method": "gist", "with": {}}, "idx_user_addresses_user_default": {"name": "idx_user_addresses_user_default", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_default", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_addresses_province": {"name": "idx_user_addresses_province", "columns": [{"expression": "province", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_addresses_city": {"name": "idx_user_addresses_city", "columns": [{"expression": "city", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_addresses_district": {"name": "idx_user_addresses_district", "columns": [{"expression": "district", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_addresses_geo_hierarchy": {"name": "idx_user_addresses_geo_hierarchy", "columns": [{"expression": "province", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "district", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "city", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_addresses_user_id_users_id_fk": {"name": "user_addresses_user_id_users_id_fk", "tableFrom": "user_addresses", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_addresses_id_unique": {"name": "user_addresses_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.order_assignments": {"name": "order_assignments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "service_personnel_id": {"name": "service_personnel_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "shop_id": {"name": "shop_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "assignment_type": {"name": "assignment_type", "type": "assignment_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_order_assignments_personnel_time": {"name": "idx_order_assignments_personnel_time", "columns": [{"expression": "service_personnel_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "assigned_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_order_assignments_shop_time": {"name": "idx_order_assignments_shop_time", "columns": [{"expression": "shop_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "assigned_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "where": "shop_id IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "idx_order_assignments_type_time": {"name": "idx_order_assignments_type_time", "columns": [{"expression": "assignment_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "assigned_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"order_assignments_order_id_orders_id_fk": {"name": "order_assignments_order_id_orders_id_fk", "tableFrom": "order_assignments", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "order_assignments_service_personnel_id_service_personnel_user_id_fk": {"name": "order_assignments_service_personnel_id_service_personnel_user_id_fk", "tableFrom": "order_assignments", "tableTo": "service_personnel", "columnsFrom": ["service_personnel_id"], "columnsTo": ["user_id"], "onDelete": "cascade", "onUpdate": "no action"}, "order_assignments_shop_id_shops_id_fk": {"name": "order_assignments_shop_id_shops_id_fk", "tableFrom": "order_assignments", "tableTo": "shops", "columnsFrom": ["shop_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"order_assignments_id_unique": {"name": "order_assignments_id_unique", "nullsNotDistinct": false, "columns": ["id"]}, "order_assignments_order_id_unique": {"name": "order_assignments_order_id_unique", "nullsNotDistinct": false, "columns": ["order_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "order_serial": {"name": "order_serial", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "service_id": {"name": "service_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "address_id": {"name": "address_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "order_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending_payment'"}, "original_amount": {"name": "original_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "coupon_code": {"name": "coupon_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "appointment_time": {"name": "appointment_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_orders_customer_status_time": {"name": "idx_orders_customer_status_time", "columns": [{"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_orders_status_appointment": {"name": "idx_orders_status_appointment", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "appointment_time", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "status IN ('pending_assignment', 'service_in_progress')", "concurrently": false, "method": "btree", "with": {}}, "idx_orders_service_time": {"name": "idx_orders_service_time", "columns": [{"expression": "service_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {"fillfactor": "70"}}, "idx_orders_serial_lookup": {"name": "idx_orders_serial_lookup", "columns": [{"expression": "order_serial", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"orders_customer_id_users_id_fk": {"name": "orders_customer_id_users_id_fk", "tableFrom": "orders", "tableTo": "users", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "orders_service_id_services_id_fk": {"name": "orders_service_id_services_id_fk", "tableFrom": "orders", "tableTo": "services", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "orders_address_id_user_addresses_id_fk": {"name": "orders_address_id_user_addresses_id_fk", "tableFrom": "orders", "tableTo": "user_addresses", "columnsFrom": ["address_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"orders_id_unique": {"name": "orders_id_unique", "nullsNotDistinct": false, "columns": ["id"]}, "orders_order_serial_unique": {"name": "orders_order_serial_unique", "nullsNotDistinct": false, "columns": ["order_serial"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payments": {"name": "payments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "payment_method": {"name": "payment_method", "type": "payment_method", "typeSchema": "public", "primaryKey": false, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "paid_at": {"name": "paid_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_payments_order_status": {"name": "idx_payments_order_status", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_payments_status_time": {"name": "idx_payments_status_time", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "paid_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "paid_at IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "idx_payments_transaction_id": {"name": "idx_payments_transaction_id", "columns": [{"expression": "transaction_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "transaction_id IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payments_order_id_orders_id_fk": {"name": "payments_order_id_orders_id_fk", "tableFrom": "payments", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"payments_id_unique": {"name": "payments_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.blocks": {"name": "blocks", "schema": "", "columns": {"blocker_id": {"name": "blocker_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "blocked_id": {"name": "blocked_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {"idx_blocks_blocker": {"name": "idx_blocks_blocker", "columns": [{"expression": "blocker_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "blocked_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_blocks_blocked": {"name": "idx_blocks_blocked", "columns": [{"expression": "blocked_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "blocker_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"blocks_blocker_id_users_id_fk": {"name": "blocks_blocker_id_users_id_fk", "tableFrom": "blocks", "tableTo": "users", "columnsFrom": ["blocker_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "blocks_blocked_id_users_id_fk": {"name": "blocks_blocked_id_users_id_fk", "tableFrom": "blocks", "tableTo": "users", "columnsFrom": ["blocked_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"blocks_pkey": {"name": "blocks_pkey", "columns": ["blocker_id", "blocked_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.follows": {"name": "follows", "schema": "", "columns": {"follower_id": {"name": "follower_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "following_id": {"name": "following_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {"idx_follows_follower": {"name": "idx_follows_follower", "columns": [{"expression": "follower_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "following_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_follows_following": {"name": "idx_follows_following", "columns": [{"expression": "following_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "follower_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"follows_follower_id_users_id_fk": {"name": "follows_follower_id_users_id_fk", "tableFrom": "follows", "tableTo": "users", "columnsFrom": ["follower_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "follows_following_id_users_id_fk": {"name": "follows_following_id_users_id_fk", "tableFrom": "follows", "tableTo": "users", "columnsFrom": ["following_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"follows_pkey": {"name": "follows_pkey", "columns": ["follower_id", "following_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reviews": {"name": "reviews", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "reviewer_id": {"name": "reviewer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "target_id": {"name": "target_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "target_type": {"name": "target_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_reviews_target_rating": {"name": "idx_reviews_target_rating", "columns": [{"expression": "target_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "target_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "rating", "isExpression": false, "asc": false, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_reviews_order_unique": {"name": "idx_reviews_order_unique", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_reviews_reviewer_time": {"name": "idx_reviews_reviewer_time", "columns": [{"expression": "reviewer_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"reviews_order_id_orders_id_fk": {"name": "reviews_order_id_orders_id_fk", "tableFrom": "reviews", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "reviews_reviewer_id_users_id_fk": {"name": "reviews_reviewer_id_users_id_fk", "tableFrom": "reviews", "tableTo": "users", "columnsFrom": ["reviewer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"reviews_id_unique": {"name": "reviews_id_unique", "nullsNotDistinct": false, "columns": ["id"]}, "reviews_order_id_unique": {"name": "reviews_order_id_unique", "nullsNotDistinct": false, "columns": ["order_id"]}}, "policies": {}, "checkConstraints": {"rating_check": {"name": "rating_check", "value": "\"reviews\".\"rating\" >= 1 AND \"reviews\".\"rating\" <= 5"}}, "isRLSEnabled": false}, "public.earnings": {"name": "earnings", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_earnings_user_time": {"name": "idx_earnings_user_time", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_earnings_order_user": {"name": "idx_earnings_order_user", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_earnings_amount_time": {"name": "idx_earnings_amount_time", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}, {"expression": "amount", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "amount > 0", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"earnings_order_id_orders_id_fk": {"name": "earnings_order_id_orders_id_fk", "tableFrom": "earnings", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "earnings_user_id_users_id_fk": {"name": "earnings_user_id_users_id_fk", "tableFrom": "earnings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"earnings_id_unique": {"name": "earnings_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.withdrawals": {"name": "withdrawals", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "withdrawal_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "requested_at": {"name": "requested_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "processed_at": {"name": "processed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_withdrawals_user_status": {"name": "idx_withdrawals_user_status", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "requested_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_withdrawals_status_time": {"name": "idx_withdrawals_status_time", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "requested_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "where": "status IN ('pending', 'approved')", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"withdrawals_user_id_users_id_fk": {"name": "withdrawals_user_id_users_id_fk", "tableFrom": "withdrawals", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"withdrawals_id_unique": {"name": "withdrawals_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "notification_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_notifications_user_unread": {"name": "idx_notifications_user_unread", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_read", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_type_time": {"name": "idx_notifications_type_time", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_user_type": {"name": "idx_notifications_user_type", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_read", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"notifications_id_unique": {"name": "notifications_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.coupon_category_restrictions": {"name": "coupon_category_restrictions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "coupon_id": {"name": "coupon_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}}, "indexes": {"idx_coupon_category_coupon_id": {"name": "idx_coupon_category_coupon_id", "columns": [{"expression": "coupon_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_coupon_category_unique": {"name": "idx_coupon_category_unique", "columns": [{"expression": "coupon_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "category_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"coupon_category_restrictions_coupon_id_coupons_id_fk": {"name": "coupon_category_restrictions_coupon_id_coupons_id_fk", "tableFrom": "coupon_category_restrictions", "tableTo": "coupons", "columnsFrom": ["coupon_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "coupon_category_restrictions_category_id_service_categories_id_fk": {"name": "coupon_category_restrictions_category_id_service_categories_id_fk", "tableFrom": "coupon_category_restrictions", "tableTo": "service_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"coupon_category_restrictions_id_unique": {"name": "coupon_category_restrictions_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.coupon_service_restrictions": {"name": "coupon_service_restrictions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "coupon_id": {"name": "coupon_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "service_id": {"name": "service_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}}, "indexes": {"idx_coupon_service_coupon_id": {"name": "idx_coupon_service_coupon_id", "columns": [{"expression": "coupon_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_coupon_service_unique": {"name": "idx_coupon_service_unique", "columns": [{"expression": "coupon_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "service_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"coupon_service_restrictions_coupon_id_coupons_id_fk": {"name": "coupon_service_restrictions_coupon_id_coupons_id_fk", "tableFrom": "coupon_service_restrictions", "tableTo": "coupons", "columnsFrom": ["coupon_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "coupon_service_restrictions_service_id_services_id_fk": {"name": "coupon_service_restrictions_service_id_services_id_fk", "tableFrom": "coupon_service_restrictions", "tableTo": "services", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"coupon_service_restrictions_id_unique": {"name": "coupon_service_restrictions_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.coupon_usage_records": {"name": "coupon_usage_records", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_coupon_id": {"name": "user_coupon_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "order_id": {"name": "order_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "original_amount": {"name": "original_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "final_amount": {"name": "final_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "used_at": {"name": "used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_coupon_usage_user_coupon": {"name": "idx_coupon_usage_user_coupon", "columns": [{"expression": "user_coupon_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_coupon_usage_order": {"name": "idx_coupon_usage_order", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_coupon_usage_used_at": {"name": "idx_coupon_usage_used_at", "columns": [{"expression": "used_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"coupon_usage_records_user_coupon_id_user_coupons_id_fk": {"name": "coupon_usage_records_user_coupon_id_user_coupons_id_fk", "tableFrom": "coupon_usage_records", "tableTo": "user_coupons", "columnsFrom": ["user_coupon_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "coupon_usage_records_order_id_orders_id_fk": {"name": "coupon_usage_records_order_id_orders_id_fk", "tableFrom": "coupon_usage_records", "tableTo": "orders", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"coupon_usage_records_id_unique": {"name": "coupon_usage_records_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {"discount_amount_positive": {"name": "discount_amount_positive", "value": "\"coupon_usage_records\".\"discount_amount\" >= 0"}, "final_amount_non_negative": {"name": "final_amount_non_negative", "value": "\"coupon_usage_records\".\"final_amount\" >= 0"}}, "isRLSEnabled": false}, "public.coupons": {"name": "coupons", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "coupon_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "discount_value": {"name": "discount_value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "min_order_amount": {"name": "min_order_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "max_discount_amount": {"name": "max_discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "usage_limit": {"name": "usage_limit", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "total_usage_limit": {"name": "total_usage_limit", "type": "integer", "primaryKey": false, "notNull": false}, "current_usage_count": {"name": "current_usage_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_multi_use": {"name": "is_multi_use", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "valid_from": {"name": "valid_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "valid_until": {"name": "valid_until", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "coupon_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_coupons_code": {"name": "idx_coupons_code", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "idx_coupons_status_valid": {"name": "idx_coupons_status_valid", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "valid_from", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "valid_until", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"coupons_created_by_users_id_fk": {"name": "coupons_created_by_users_id_fk", "tableFrom": "coupons", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"coupons_id_unique": {"name": "coupons_id_unique", "nullsNotDistinct": false, "columns": ["id"]}, "coupons_code_unique": {"name": "coupons_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {"discount_value_positive": {"name": "discount_value_positive", "value": "\"coupons\".\"discount_value\" > 0"}, "min_order_amount_non_negative": {"name": "min_order_amount_non_negative", "value": "\"coupons\".\"min_order_amount\" >= 0"}, "valid_period_check": {"name": "valid_period_check", "value": "\"coupons\".\"valid_from\" < \"coupons\".\"valid_until\""}}, "isRLSEnabled": false}, "public.user_coupons": {"name": "user_coupons", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "coupon_id": {"name": "coupon_id", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "user_coupon_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'available'"}, "used_count": {"name": "used_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "obtained_at": {"name": "obtained_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "first_used_at": {"name": "first_used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "last_used_at": {"name": "last_used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_user_coupons_user_id": {"name": "idx_user_coupons_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_coupons_coupon_id": {"name": "idx_user_coupons_coupon_id", "columns": [{"expression": "coupon_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_coupons_status": {"name": "idx_user_coupons_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_coupons_user_status": {"name": "idx_user_coupons_user_status", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_coupons_user_id_users_id_fk": {"name": "user_coupons_user_id_users_id_fk", "tableFrom": "user_coupons", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_coupons_coupon_id_coupons_id_fk": {"name": "user_coupons_coupon_id_coupons_id_fk", "tableFrom": "user_coupons", "tableTo": "coupons", "columnsFrom": ["coupon_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_coupons_id_unique": {"name": "user_coupons_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.china_city": {"name": "china_city", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "pid": {"name": "pid", "type": "integer", "primaryKey": false, "notNull": true}, "deep": {"name": "deep", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "pinyin_prefix": {"name": "pinyin_prefix", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "pinyin": {"name": "pinyin", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "ext_id": {"name": "ext_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "ext_name": {"name": "ext_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {"idx_accounts_pid": {"name": "idx_accounts_pid", "columns": [{"expression": "pid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_accounts_name": {"name": "idx_accounts_name", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_accounts_pinyin_prefix": {"name": "idx_accounts_pinyin_prefix", "columns": [{"expression": "pinyin_prefix", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_accounts_pinyin": {"name": "idx_accounts_pinyin", "columns": [{"expression": "pinyin", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_accounts_deep": {"name": "idx_accounts_deep", "columns": [{"expression": "deep", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_accounts_ext_id": {"name": "idx_accounts_ext_id", "columns": [{"expression": "ext_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.coupon_status": {"name": "coupon_status", "schema": "public", "values": ["active", "inactive", "expired", "disabled"]}, "public.coupon_type": {"name": "coupon_type", "schema": "public", "values": ["fixed_amount", "percentage", "free_shipping"]}, "public.user_coupon_status": {"name": "user_coupon_status", "schema": "public", "values": ["available", "used", "expired"]}, "public.assignment_type": {"name": "assignment_type", "schema": "public", "values": ["system_auto", "shop_dispatch", "customer_designated", "grab"]}, "public.notification_type": {"name": "notification_type", "schema": "public", "values": ["system", "order_update", "promotion"]}, "public.order_status": {"name": "order_status", "schema": "public", "values": ["pending_payment", "pending_assignment", "service_in_progress", "pending_acceptance", "pending_review", "completed", "cancelled", "refunded"]}, "public.payment_method": {"name": "payment_method", "schema": "public", "values": ["wechat_pay", "alipay", "bank_transfer"]}, "public.payment_status": {"name": "payment_status", "schema": "public", "values": ["pending", "succeeded", "failed"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["customer", "service_personnel", "shop_admin", "admin", "super_admin"]}, "public.withdrawal_status": {"name": "withdrawal_status", "schema": "public", "values": ["pending", "approved", "rejected", "completed"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}