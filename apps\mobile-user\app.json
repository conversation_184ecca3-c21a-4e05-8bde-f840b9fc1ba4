{"expo": {"name": "mobile-user", "slug": "mobile-user", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "home-server-user", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.zhang3.mobileuser"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "package": "com.zhang3.mobileuser"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["./plugins/withAndroidSignature", "expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-secure-store", {"configureAndroidBackup": true, "faceIDPermission": "Allow $(PRODUCT_NAME) to access your Face ID biometric data."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "当前应用需要访问您的位置，已获取你的服务地址"}], ["expo-qq-location", {"apiKey": "WLEBZ-HCTC3-JMN3L-OLTJV-R2AJE-WCB7J"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "56004597-0fe8-4294-9aad-4566216d42bf"}}}}