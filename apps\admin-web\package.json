{"name": "admin-web", "version": "0.1.0", "private": true, "scripts": {"dev": "dotenvx run -f .env.development -- next dev --turbopack", "build": "dotenvx run -f .env.production -- next build", "start": "dotenvx run -f .env.production -- next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.0", "@repo/web-ui": "workspace:*", "@repo/utils": "workspace:*", "next": "15.4.4", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@repo/eslint-config": "workspace:*", "@repo/types": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}