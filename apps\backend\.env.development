
# ===== 来自 env/development/.env.api =====

# ===== 来自 env/development/.env.api =====
# ===========================================
# 开发环境 - API配置 (仅限api项目可访问)
# ===========================================


# 数据库配置
DATABASE_URI="postgresql://root:root@localhost:15432/home_server"

# 阿里云配置
ALI_ACCESS_KEY="LTAI5tSieqLKGReHGyvMbsTD"
ALI_ACCESSKEY_SECRET="******************************"
ARN="acs:ram::1483489217946252:role/yxsz1"

# 域名配置
DOMAIN_NAME=127.0.0.1:5050
NEXT_PUBLIC_WEB_HOST=http://localhost:3000

# S3配置
s3_NEDPOINT=http://localhost:9000
s3_ACCESS_KEY=root
s3_ACCESS_SECRET="G?cue>NY=D+p%QEuc#5g"
# Redis配置
REDIS_CLIENT_HOST=redis://127.0.0.1
REDIS_CLIENT_PORT=6379
REDIS_CLIENT_DB=0

# 认证配置
BETTER_AUTH_SECRET=HUGe6x7FjPbAzmZOWKoHurlVcbwch4Df
BETTER_AUTH_URL=http://localhost:3000
TRUSTED_ORIGINS="http://localhost:3000,home-server-user://"
CROSS_DOMAIN_ORIGIN=".example.com"
EMAIL_VERIFICATION_PAGE_PATH=/auth/verify-email
PASSWORD_RESET_PAGE_PATH=/auth/reset-password

# 邮件配置
MAIL_HOST=smtp.qq.com
MAIL_PORT=465
MAIL_USER=<EMAIL>
MAIL_PASS=uekzxgidhivedihd
MAIL_FROM_NAME="Cow Course"

# 允许的IP地址列表（逗号分隔，可选）
# INTERNAL_ALLOWED_IPS=*************,*********

# 是否允许本地IP访问（默认: true）
INTERNAL_ALLOW_LOCAL_IPS=true

# 是否允许私有网络IP访问（默认: true）
INTERNAL_ALLOW_PRIVATE_IPS=true

# 是否需要内部API密钥验证（默认: false）
INTERNAL_REQUIRE_API_KEY=false

# 内部API密钥（当启用API密钥验证时设置）
# INTERNAL_API_KEY=your-secret-internal-key

# 内部API密钥环境变量名（默认: INTERNAL_API_KEY）
# INTERNAL_API_KEY_ENV_VAR=INTERNAL_API_KEY

# API配置
NEXT_PUBLIC_API_URL=http://localhost:5050/api

# GitHub OAuth配置
GITHUB_CLIENT_ID=********************
GITHUB_CLIENT_SECRET=ee7b7a81c3c214c2a72ba94dd514bcde1d326a0c

# 微信OAuth配置
WECHAT_CLIENT_ID=wxafad80cfc0ca799b
WECHAT_CLIENT_SECRET=1f6efcfe8021ce208115710195a421f5

# MeiliSearch 配置(全文搜索)
MEILI_HOST=http://127.0.0.1:7700
MEILI_MASTER_KEY=HZnDQKHkCEeny3FsfYPBDfe6g5906w

# 腾讯地图密钥

TMAP_KEY=KEGBZ-HGBW3-UYS3G-RMZ73-WVRTO-DLF6L
TMAP_SECRET=LxfdfFg0kuGYbfCTy29rq3KufO3tTcJ6

# ===== 来自 env/development/.env.common =====
# ===========================================
# 开发环境 - 公共配置 (所有项目可访问)
# ===========================================

# 域名配置
DOMAIN_NAME=*************:5050
NEXT_PUBLIC_WEB_HOST=http://*************:5050

# API配置
NEXT_PUBLIC_API_URL=http://*************:5050/api


