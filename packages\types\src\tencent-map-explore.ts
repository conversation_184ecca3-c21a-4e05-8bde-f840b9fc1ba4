import { z } from 'zod/v4';
import {
    ExploreRequestSchema,
    ExploreResponseSchema,
    ExploreBoundarySchema,
    type ExploreRequest,
    type ExploreResponse,
    type ExploreBoundary,
    type ExplorePoi,
} from './address';

/**
 * 腾讯地图周边推荐（explore）API服务
 * 
 * 提供无需关键词的周边高热度地点搜索功能，适用于：
 * - 地点签到场景
 * - 位置共享场景
 * - 发送位置场景
 */

/**
 * 构建周边推荐API的boundary参数
 * @param params 边界参数对象
 * @returns 格式化的boundary字符串
 * 
 * @example
 * ```typescript
 * const boundary = buildExploreBoundary({
 *   lat: 40.040394,
 *   lng: 116.273523,
 *   radius: 1000,
 *   auto_extend: '1'
 * });
 * // 返回: "nearby(40.040394,116.273523,1000,1)"
 * ```
 */
export function buildExploreBoundary(params: ExploreBoundary): string {
    const { lat, lng, radius, auto_extend } = ExploreBoundarySchema.parse(params);
    
    if (auto_extend) {
        return `nearby(${lat},${lng},${radius},${auto_extend})`;
    }
    return `nearby(${lat},${lng},${radius})`;
}

/**
 * 腾讯地图周边推荐API请求函数
 * @param params 请求参数
 * @returns Promise<ExploreResponse> API响应结果
 * 
 * @example
 * ```typescript
 * // 基础用法
 * const result = await exploreNearbyPlaces({
 *   key: 'your-api-key',
 *   boundary: 'nearby(40.040394,116.273523,1000)',
 *   policy: '1',
 *   page_size: 10
 * });
 * 
 * // 使用辅助函数构建boundary
 * const boundary = buildExploreBoundary({
 *   lat: 40.040394,
 *   lng: 116.273523,
 *   radius: 1000,
 *   auto_extend: '1'
 * });
 * 
 * const result = await exploreNearbyPlaces({
 *   key: 'your-api-key',
 *   boundary,
 *   policy: '2', // 位置共享场景
 *   filter: 'category=美食,购物',
 *   address_format: 'short'
 * });
 * ```
 */
export async function exploreNearbyPlaces(params: ExploreRequest): Promise<ExploreResponse> {
    // 验证请求参数
    const validatedParams = ExploreRequestSchema.parse(params);
    
    // 构建请求URL
    const baseUrl = 'https://apis.map.qq.com/ws/place/v1/explore';
    const searchParams = new URLSearchParams();
    
    // 添加必需参数
    searchParams.append('key', validatedParams.key);
    searchParams.append('boundary', validatedParams.boundary);
    
    // 添加可选参数
    if (validatedParams.policy) {
        searchParams.append('policy', validatedParams.policy);
    }
    if (validatedParams.filter) {
        searchParams.append('filter', validatedParams.filter);
    }
    if (validatedParams.orderby) {
        searchParams.append('orderby', validatedParams.orderby);
    }
    if (validatedParams.address_format) {
        searchParams.append('address_format', validatedParams.address_format);
    }
    if (validatedParams.page_size) {
        searchParams.append('page_size', validatedParams.page_size.toString());
    }
    if (validatedParams.page_index) {
        searchParams.append('page_index', validatedParams.page_index.toString());
    }
    if (validatedParams.output) {
        searchParams.append('output', validatedParams.output);
    }
    if (validatedParams.callback) {
        searchParams.append('callback', validatedParams.callback);
    }
    
    const requestUrl = `${baseUrl}?${searchParams.toString()}`;
    
    try {
        // 发送HTTP请求
        const response = await fetch(requestUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'TencentMapExploreAPI/1.0',
            },
        });
        
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // 验证响应数据
        const validatedResponse = ExploreResponseSchema.parse(data);
        
        // 检查API状态码
        if (validatedResponse.status !== 0) {
            throw new Error(`腾讯地图API错误: ${validatedResponse.message} (状态码: ${validatedResponse.status})`);
        }
        
        return validatedResponse;
        
    } catch (error) {
        if (error instanceof z.ZodError) {
            throw new Error(`数据验证失败: ${error.errors.map(e => e.message).join(', ')}`);
        }
        
        if (error instanceof Error) {
            throw error;
        }
        
        throw new Error('未知错误occurred during API request');
    }
}

/**
 * 简化的周边推荐API调用函数
 * @param key API密钥
 * @param lat 纬度
 * @param lng 经度
 * @param radius 搜索半径（米）
 * @param options 可选参数
 * @returns Promise<ExplorePoi[]> POI列表
 * 
 * @example
 * ```typescript
 * // 简单调用
 * const pois = await exploreNearby('your-api-key', 40.040394, 116.273523, 1000);
 * 
 * // 带选项调用
 * const pois = await exploreNearby('your-api-key', 40.040394, 116.273523, 1000, {
 *   policy: '2',
 *   filter: 'category=美食',
 *   page_size: 20
 * });
 * ```
 */
export async function exploreNearby(
    key: string,
    lat: number,
    lng: number,
    radius: number,
    options: Partial<Omit<ExploreRequest, 'key' | 'boundary'>> = {}
): Promise<ExplorePoi[]> {
    const boundary = buildExploreBoundary({ lat, lng, radius });
    
    const response = await exploreNearbyPlaces({
        key,
        boundary,
        ...options,
    });
    
    return response.data;
}

/**
 * 导出所有相关类型和schema，便于外部使用
 */
export {
    ExploreRequestSchema,
    ExploreResponseSchema,
    ExploreBoundarySchema,
    type ExploreRequest,
    type ExploreResponse,
    type ExploreBoundary,
    type ExplorePoi,
};
