// Learn more: https://docs.expo.dev/guides/monorepos/
const { getDefaultConfig } = require("expo/metro-config");
const { FileStore } = require('@expo/metro-config/file-store');
const { withNativeWind } = require('nativewind/metro');

const path = require("node:path");

const config = withTurborepoManagedCache(
    getDefaultConfig(__dirname, { isCSSEnabled: true })
);
module.exports = withNativeWind(config, { input: "../../packages/mobile-ui/src/styles/global.css" });

/**
 * Move the Metro cache to the `.cache/metro` folder.
 * If you have any environment variables, you can configure Turborepo to invalidate it when needed.
 *
 * @see https://turborepo.com/docs/reference/configuration#env
 * @param {import('expo/metro-config').MetroConfig} config
 * @returns {import('expo/metro-config').MetroConfig}
 */
function withTurborepoManagedCache(config) {
  config.cacheStores = [
    new FileStore({ root: path.join(__dirname, ".cache/metro") }),
  ];
    config.resolver.unstable_enablePackageExports = true;
  return config;
}