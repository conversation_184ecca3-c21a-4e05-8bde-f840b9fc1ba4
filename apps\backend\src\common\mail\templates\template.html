<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账户验证邮件</title>
       <style>
        /* 基础样式 */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f9fc;
            margin: 0;
            padding: 0;
            color: #333;
            line-height: 1.6;
        }

        /* 邮件容器 */
        .email-container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        /* 头部样式 */
        .email-header {
            background: linear-gradient(135deg, #96D6AA 0%, #7cc091 100%);
            padding: 30px 20px 20px;
            text-align: center;
            color: white;
            position: relative;
        }

        /* Logo容器 */
        .logo-container {
            display: inline-block;
            background-color: white;
            border-radius: 50%;
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            border: 4px solid white;
        }

        .logo-container img {
            max-width: 80px;
            max-height: 80px;
            display: block;
        }

        .email-title {
            font-size: 24px;
            margin: 10px 0;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            max-width: 80%;
            margin: 0 auto;
        }

        /* 内容区域 */
        .email-content {
            padding: 30px;
            color: #555;
        }

        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
        }

        .instructions {
            background-color: #f8faf9;
            border-left: 4px solid #96D6AA;
            padding: 15px 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        /* 按钮样式 */
        .action-button {
            display: block;
            width: 80%;
            max-width: 300px;
            background-color: #96D6AA;
            color: white !important;
            text-align: center;
            padding: 16px 0;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            font-size: 18px;
            margin: 30px auto;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(150, 214, 170, 0.3);
        }

        .action-button:hover {
            background-color: #7fc098;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(150, 214, 170, 0.4);
        }

        /* 链接备用 */
        .link-backup {
            text-align: center;
            font-size: 14px;
            color: #777;
            margin: 20px 0;
            padding: 15px;
            background-color: #f8faf9;
            border-radius: 8px;
        }

        .link-backup a {
            color: #96D6AA;
            word-break: break-all;
            text-decoration: none;
            font-weight: 500;
        }

        /* 页脚 */
        .email-footer {
            background-color: #f0f5f2;
            padding: 25px;
            text-align: center;
            font-size: 14px;
            color: #666;
            border-top: 1px solid #e6e6e6;
        }

        .contact-info {
            margin: 15px 0;
        }

        .social-links {
            margin: 20px 0;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #96D6AA;
            text-decoration: none;
            font-weight: 500;
        }

        .copyright {
            margin-top: 20px;
            color: #888;
            font-size: 13px;
        }

        /* 响应式设计 */
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }

            .email-content {
                padding: 20px;
            }

            .action-button {
                width: 90%;
                padding: 14px 0;
            }

            .logo-container {
                width: 80px;
                height: 80px;
            }

            .logo-container img {
                max-width: 60px;
                max-height: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
 <div class="email-header">
            <!-- Logo位置 -->
            <div class="logo-container">
                <img src={{logo}} alt="产品Logo">
            </div>

            <div class="email-title">请验证您的账户</div>
            <div class="header-subtitle">安全验证步骤保障您的账户安全</div>
        </div>

        <div class="email-content">
            <div class="greeting">
                <p>尊敬的<strong>{{userName}}</strong>，您好！</p>
            </div>

            <p>我们收到了您对<strong>{{productName}}</strong>的{{registerOrRester}}请求。为确保账户安全，请完成以下验证步骤：</p>

            <div class="instructions">
                <p>请点击下方按钮验证您的账户。该链接将在<strong>{{expiredTime}}</strong>后失效。</p>
            </div>

            <a href="{{url}}" class="action-button">
                {{buttonText}}
            </a>

            <div class="link-backup">
                <p>如果以上按钮无法点击，请复制以下链接到浏览器地址栏访问：</p>
                <p><a href="{{url}}">{{url}}</a></p>
            </div>

            <p>如果您没有进行此操作，请忽略此邮件，您的账户仍然是安全的。</p>
            <p>感谢您选择我们的服务！</p>
        </div>
    </div>
</body>
</html>
