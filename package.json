{"name": "home-server-monorepo", "private": true, "description": "Home service platform monorepo with backend API, mobile apps, and admin web interface", "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "type-check": "turbo type-check", "test": "turbo test", "test:watch": "turbo test:watch", "format": "turbo format", "clean": "turbo clean", "ci": "turbo build test lint type-check", "storybook": "turbo storybook --filter=@repo/web-ui", "build-storybook": "turbo build-storybook --filter=@repo/web-ui", "deploy": "turbo deploy", "start": "turbo start", "format:prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "env:setup": "node env/scripts/setup-env-links.js", "backend:dev": "dotenvx run -f apps/backend/.env.development -- turbo dev --filter=backend", "backend:build": "dotenvx run -f apps/backend/.env.production -- turbo build --filter=backend", "backend:start": "dotenvx run -f apps/backend/.env.production -- turbo start --filter=backend", "admin:dev": "dotenvx run -f apps/admin-web/.env.development -- turbo dev --filter=admin-web", "admin:build": "dotenvx run -f apps/admin-web/.env.production -- turbo build --filter=admin-web", "admin:start": "dotenvx run -f apps/admin-web/.env.production -- turbo start --filter=admin-web", "mobile-user:dev": "dotenvx run -f apps/mobile-user/.env.development -- turbo dev --filter=mobile-user", "mobile-user:build": "dotenvx run -f apps/mobile-user/.env.production -- turbo build --filter=mobile-user", "mobile-user:android": "dotenvx run -f apps/mobile-user/.env.development -- turbo android --filter=mobile-user", "mobile-user:ios": "dotenvx run -f apps/mobile-user/.env.development -- turbo ios --filter=mobile-user", "mobile-worker:dev": "dotenvx run -f apps/mobile-worker/.env.development -- turbo dev --filter=mobile-worker", "mobile-worker:build": "dotenvx run -f apps/mobile-worker/.env.production -- turbo build --filter=mobile-worker", "mobile-worker:android": "dotenvx run -f apps/mobile-worker/.env.development -- turbo android --filter=mobile-worker", "mobile-worker:ios": "dotenvx run -f apps/mobile-worker/.env.development -- turbo ios --filter=mobile-worker", "ui:dev": "turbo dev --filter=@repo/web-ui", "ui:build": "turbo build --filter=@repo/web-ui"}, "devDependencies": {"@dotenvx/dotenvx": "^1.48.3", "prettier": "^3.6.2", "turbo": "^2.5.5", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18", "pnpm": ">=9.0.0"}, "workspaces": ["apps/*", "packages/*"], "dependencies": {"@better-auth/expo": "^1.3.4", "better-auth": "^1.3.4", "ofetch": "^1.4.1", "tsup": "^8.5.0", "zod": "^4.0.10"}}