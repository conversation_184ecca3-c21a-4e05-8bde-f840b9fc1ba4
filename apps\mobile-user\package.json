{"name": "mobile-user", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"dev": "dotenvx run -f .env.development -- expo start", "start": "dotenvx run -f .env.production -- expo start", "build": "dotenvx run -f .env.production -- expo export", "android": "dotenvx run -f .env.development -- expo run:android", "ios": "dotenvx run -f .env.development -- expo start --ios", "web": "dotenvx run -f .env.development -- expo start --web", "lint": "expo lint", "type-check": "tsc --noEmit", "clean": "expo r -c", "reset-project": "node ./scripts/reset-project.js"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.2.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@repo/mobile-ui": "workspace:*", "@repo/types": "workspace:*", "@repo/utils": "workspace:*", "@rn-primitives/accordion": "^1.0.4", "@rn-primitives/alert-dialog": "^1.2.0", "@rn-primitives/aspect-ratio": "^1.2.0", "@rn-primitives/avatar": "^1.2.0", "@rn-primitives/checkbox": "^1.2.0", "@rn-primitives/collapsible": "^1.2.0", "@rn-primitives/context-menu": "^1.2.0", "@rn-primitives/dialog": "^1.2.0", "@rn-primitives/dropdown-menu": "^1.2.0", "@rn-primitives/hover-card": "^1.2.0", "@rn-primitives/label": "^1.2.0", "@rn-primitives/menubar": "^1.2.0", "@rn-primitives/navigation-menu": "^1.2.0", "@rn-primitives/popover": "^1.2.0", "@rn-primitives/progress": "^1.2.0", "@rn-primitives/radio-group": "^1.2.0", "@rn-primitives/select": "^1.2.0", "@rn-primitives/separator": "^1.2.0", "@rn-primitives/slot": "^1.2.0", "@rn-primitives/switch": "^1.2.0", "@rn-primitives/table": "^1.2.0", "@rn-primitives/tabs": "^1.2.0", "@rn-primitives/toggle": "^1.2.0", "@rn-primitives/toggle-group": "^1.2.0", "@rn-primitives/tooltip": "^1.2.0", "@shopify/flash-list": "^1.7.6", "@tanstack/react-query": "^5.85.0", "expo": "~53.0.20", "expo-blur": "~14.1.5", "expo-clipboard": "^7.1.5", "expo-constants": "~17.1.7", "expo-dev-client": "^5.2.4", "expo-device": "~7.1.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linking": "~7.1.7", "expo-location": "^18.1.6", "expo-network": "^7.1.5", "expo-qq-location": "^0.1.1", "expo-router": "~5.1.4", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "gcoord": "^1.0.7", "immer": "^10.1.1", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.62.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-mmkv": "^3.3.0", "react-native-react-query-devtools": "^1.5.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "sonner-native": "^0.21.0", "tlbs-map-react": "^1.1.0", "zustand": "^5.0.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/react": "~19.0.14", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "prettier-plugin-tailwindcss": "0.5.11", "tailwindcss": "3.4.17", "typescript": "~5.8.3"}, "overrides": {"@types/react": "19.0.0"}, "private": true}