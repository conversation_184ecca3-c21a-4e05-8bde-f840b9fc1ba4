{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "dev": "dotenvx run -f .env.development -- nest start --watch", "start": "dotenvx run -f .env.production -- nest start", "start:debug": "dotenvx run -f .env.development -- nest start --debug --watch", "start:prod": "dotenvx run -f .env.production -- node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:generate": "pnpm drizzle-kit generate", "db:migration": "dotenvx run -f .env.development -- pnpm drizzle-kit migrate", "db:generate:migrate": "pnpm drizzle-kit generate && dotenvx run -f .env.development -- pnpm drizzle-kit migrate"}, "dependencies": {"@anatine/zod-nestjs": "^2.0.12", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.2.0", "@paralleldrive/cuid2": "^2.2.2", "@repo/types": "workspace:*", "@repo/utils": "workspace:*", "@scalar/nestjs-api-reference": "^0.5.12", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "drizzle-orm": "^0.44.3", "ioredis": "^5.6.1", "nestjs-zod": "5.0.0-beta.20250731T202215", "nodemailer": "^7.0.5", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "@types/winston": "^2.4.4", "drizzle-kit": "^0.31.4", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "rimraf": "^6.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}