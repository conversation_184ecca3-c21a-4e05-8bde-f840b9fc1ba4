import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Query,
    Req,
    UsePipes,
    UseGuards,
} from '@nestjs/common';
import { AddressService } from './address.service';
import {
    AddressQuery,
    AddressQuerySchema,
    ChinaCitySchema,
    CreateUserAddress,
    GeocodeRequestSchema,
    ReverseGeocodeRequest,
    ReverseGeocodeRequestSchema,
    ReverseGeocodeResponseSchema,
    SuggestionRequest,
    SuggestionRequestSchema,
    SuggestionResponseSchema,
    UpdateUserAddress,
    UpdateUserAddressSchema,
    ParentInfoSchema,
    DistrictSearchRequest,
    DistrictSearchRequestSchema,
    DistrictSearchResponseSchema,
} from '@repo/types';
import { ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { z } from 'zod/v4';
import {
    ApiQueries,
    ApiSuccessResponse,
    ApiErrorResponses,
} from 'src/common/decorator';
import { tencentReverseGeocodeService } from './address.TencentMap.api';
import { Request } from 'express';
import { ApiBodies } from 'src/common/decorator/swagger-api-bodies';
import { CreateUserAddressSchema } from '@repo/types';
import { UserAddressesSchema } from '@repo/types';
import { ZodValidationPipe } from 'src/common/pipes';
import { AuthGuard } from '../auth/auth.guard';
import { GeocodeRequest } from '@repo/types';

@ApiTags('地址管理')
@Controller('address')
export class AddressController {
    constructor(private readonly addressService: AddressService) {}

    @UsePipes(new ZodValidationPipe(AddressQuerySchema))
    @ApiOperation({
        summary: '获取中国城市数据',
        description:
            '根据筛选条件获取中国省市区数据，支持获取全部、省份、城市或区县数据',
    })
    @ApiSuccessResponse(z.array(ChinaCitySchema), {
        description: '成功获取城市列表',
        example: {
            code: 0,
            message: '操作成功',
            data: [
                {
                    id: 1,
                    pid: 0,
                    deep: 0,
                    name: '北京',
                    pinyin_prefix: 'B',
                    pinyin: 'beijing',
                    ext_id: '110000',
                    ext_name: '北京市',
                },
            ],
            timestamp: Date.now(),
        },
    })
    @ApiErrorResponses()
    @ApiQueries(AddressQuerySchema)
    @Get('all')
    findAll(@Query() query: AddressQuery) {
        return this.addressService.findAll(query);
    }

    @UsePipes(new ZodValidationPipe(ReverseGeocodeRequestSchema))
    @ApiOperation({
        summary: '地址逆解析服务',
        description: '根据经纬度获取详细地址信息',
    })
    @Get('reverse-geocode')
    @ApiQueries(ReverseGeocodeRequestSchema)
    @ApiSuccessResponse(ReverseGeocodeResponseSchema, {
        description: '成功获取地址信息',
    })
    @ApiErrorResponses()
    async reverseGeocode(@Query() query: ReverseGeocodeRequest) {
        return await tencentReverseGeocodeService.reverseGeocodeWithValidation(
            query,
        );
    }

    @UsePipes(new ZodValidationPipe(SuggestionRequestSchema))
    @Get('suggestion')
    @ApiOperation({
        summary: '地址建议服务',
        description: '根据关键词获取地址建议列表',
    })
    @ApiQueries(SuggestionRequestSchema)
    @ApiSuccessResponse(SuggestionResponseSchema, {
        description: '成功获取详细地址信息',
    })
    @ApiErrorResponses()
    async suggestion(@Query() query: SuggestionRequest) {
        return await tencentReverseGeocodeService.getSuggestions(query);
    }

    @Get('cityParentInfo')
    @UsePipes(new ZodValidationPipe(GeocodeRequestSchema))
    @ApiOperation({
        summary: '获取城市的上级城市和省份信息',
        description: '根据城市名称获取其上级城市和省份信息',
    })
    @ApiQueries(GeocodeRequestSchema)
    @ApiSuccessResponse(ParentInfoSchema, {
        description: '成功获取城市的上级城市和省份信息',
    })
    @ApiErrorResponses()
    async getCityParentInfo(@Query() query: GeocodeRequest) {
        return await this.addressService.getCityParentInfo(query);
    }

    @UsePipes(new ZodValidationPipe(DistrictSearchRequestSchema))
    @Get('districtSearch')
    @ApiErrorResponses()
    @ApiOperation({
        summary: '搜索城市',
        description: '根据关键词搜索城市',
    })
    @ApiQueries(DistrictSearchRequestSchema)
    @ApiSuccessResponse(DistrictSearchResponseSchema, {
        description: '成功获取城市搜索结果',
    })
    async districtSearch(@Query() query: DistrictSearchRequest) {
        return await this.addressService.districtSearch(query.keyword);
    }

    @UseGuards(AuthGuard)
    @UsePipes(new ZodValidationPipe(CreateUserAddressSchema))
    @ApiOperation({
        summary: '创建用户地址',
        description: '创建用户地址',
    })
    @ApiBodies(CreateUserAddressSchema)
    @ApiSuccessResponse(UserAddressesSchema, {
        description: '成功创建用户地址',
    })
    @ApiErrorResponses()
    @Post('create')
    async createUserAddress(
        @Body() body: Omit<CreateUserAddress, 'userId'>,
        @Req() req: Request,
    ) {
        return await this.addressService.createAddress({
            ...body,
            userId: req.user?.id,
        });
    }

    @UseGuards(AuthGuard)
    @UsePipes(new ZodValidationPipe(UpdateUserAddressSchema))
    @ApiOperation({
        summary: '更新用户地址',
        description: '更新用户地址',
    })
    @ApiBodies(UpdateUserAddressSchema)
    @ApiSuccessResponse(UserAddressesSchema, {
        description: '成功更新用户地址',
    })
    @Post('update')
    async updateUserAddress(
        @Body() body: UpdateUserAddress,
        @Req() req: Request,
    ) {
        return await this.addressService.updateAddress(body.id, {
            ...body,
            userId: req.user?.id,
        });
    }

    @UseGuards(AuthGuard)
    @ApiOperation({
        summary: '删除用户地址',
        description: '删除用户地址',
    })
    @ApiParam({ name: 'id', description: '用户地址ID' })
    @Delete(':id')
    deleteUserAddress(@Param('id') id: string) {
        return this.addressService.deleteAddress(id);
    }

    @UsePipes(new ZodValidationPipe(UpdateUserAddressSchema))
    @ApiOperation({
        summary: '获取用户地址列表',
        description: '获取当前用户的所有地址(需登录后使用)',
    })
    @ApiSuccessResponse(UserAddressesSchema, {
        description: '成功获取用户地址列表',
    })
    @UseGuards(AuthGuard)
    @ApiErrorResponses()
    @Get()
    async getUserAddress(@Req() req: Request) {
        return await this.addressService.getAddressByUserId(req.user?.id);
    }
}
