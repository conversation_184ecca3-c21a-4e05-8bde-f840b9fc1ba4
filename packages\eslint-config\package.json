{"name": "@repo/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js", "./expo": "./expo.js"}, "scripts": {"lint": "eslint .", "type-check": "tsc --noEmit"}, "devDependencies": {"@eslint/js": "^9.31.0", "@next/eslint-plugin-next": "^15.4.2", "@tanstack/eslint-plugin-query": "^5.83.1", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.5.0", "globals": "^16.3.0", "typescript": "^5.8.2", "typescript-eslint": "^8.37.0"}, "dependencies": {"eslint-config-expo": "~9.2.0"}}