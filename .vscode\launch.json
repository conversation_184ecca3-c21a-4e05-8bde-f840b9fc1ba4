{"version": "0.2.0", "configurations": [{"name": "Debug API (Development)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/nest", "args": ["start", "--debug", "--watch"], "cwd": "${workspaceFolder}/apps/backend", "env": {"NODE_ENV": "development"}, "envFile": "${workspaceFolder}/apps/api/.env.development", "runtimeExecutable": "dotenvx", "runtimeArgs": ["run", "-f", ".env.development", "--"], "console": "integratedTerminal", "restart": true, "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/backend/dist/**/*.js"], "skipFiles": ["<node_internals>/**"]}, {"name": "Debug API (pnpm script)", "type": "node", "request": "launch", "runtimeExecutable": "pnpm", "runtimeArgs": ["run", "start:debug"], "cwd": "${workspaceFolder}/apps/api", "console": "integratedTerminal", "restart": true, "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/backend/dist/**/*.js"], "skipFiles": ["<node_internals>/**"]}]}