{"name": "@repo/web-ui", "version": "0.0.0", "private": true, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}, "scripts": {"lint": "eslint . --max-warnings 0"}, "devDependencies": {"tailwindcss": "^4", "@tailwindcss/postcss": "^4", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.31.0", "tsup": "^8.5.0", "typescript": "5.8.2"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@radix-ui/react-slot": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.475.0", "next-themes": "^0.4.4", "tailwind-merge": "^3.0.1", "tw-animate-css": "^1.2.4"}}